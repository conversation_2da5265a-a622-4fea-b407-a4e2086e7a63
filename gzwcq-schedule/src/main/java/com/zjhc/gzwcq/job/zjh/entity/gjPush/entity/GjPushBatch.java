package com.zjhc.gzwcq.job.zjh.entity.gjPush.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警推送批次表
 * <AUTHOR>
 * @date 2025/7/31
 */
@Data
public class GjPushBatch implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    private String id;
    
    /**
     * 批次ID
     */
    private String batchId;
    
    /**
     * 模型类型：1-模型1，2-模型2
     */
    private Integer modelType;
    
    /**
     * 总推送数量
     */
    private Integer totalCount;
    
    /**
     * 成功推送数量
     */
    private Integer successCount;
    
    /**
     * 失败推送数量
     */
    private Integer failedCount;
    
    /**
     * 跳过推送数量（去重）
     */
    private Integer skippedCount;
    
    /**
     * 批次请求数据
     */
    private String batchRequestData;
    
    /**
     * 批次响应数据
     */
    private String batchResponseData;
    
    /**
     * 批次状态：1-成功，2-失败
     */
    private Integer batchStatus;
    
    /**
     * 批次错误信息
     */
    private String batchErrorMessage;
    
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
