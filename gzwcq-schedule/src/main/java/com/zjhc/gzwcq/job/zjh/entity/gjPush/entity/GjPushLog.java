package com.zjhc.gzwcq.job.zjh.entity.gjPush.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警推送日志表
 * <AUTHOR>
 * @date 2025/7/31
 */
@Data
public class GjPushLog implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    private String id;
    
    /**
     * 批次ID，同一次推送的批次标识
     */
    private String batchId;
    
    /**
     * 模型类型：1-模型1，2-模型2
     */
    private Integer modelType;
    
    /**
     * 企业名称
     */
    private String enterpriseName;
    
    /**
     * 企业编码（统一社会信用代码）
     */
    private String enterpriseCode;
    
    /**
     * 登记ID（模型2使用）
     */
    private String registrationId;
    
    /**
     * 预警唯一标识
     */
    private String uniqueKey;
    
    /**
     * 推送请求数据
     */
    private String requestData;
    
    /**
     * 推送响应数据
     */
    private String responseData;
    
    /**
     * 推送状态：1-成功，2-失败
     */
    private Integer pushStatus;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 推送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pushTime;
}
