-- 预警推送日志表
DROP TABLE IF EXISTS `gj_push_log`;
CREATE TABLE `gj_push_log` (
  `id` varchar(50) NOT NULL COMMENT '主键',
  `batch_id` varchar(50) NOT NULL COMMENT '批次ID，同一次推送的批次标识',
  `model_type` int(2) NOT NULL COMMENT '模型类型：1-模型1，2-模型2',
  `enterprise_name` varchar(200) COMMENT '企业名称',
  `enterprise_code` varchar(100) COMMENT '企业编码（统一社会信用代码）',
  `registration_id` varchar(50) COMMENT '登记ID（模型2使用）',
  `unique_key` varchar(100) NOT NULL COMMENT '预警唯一标识',
  `request_data` text COMMENT '推送请求数据',
  `response_data` text COMMENT '推送响应数据',
  `push_status` int(2) NOT NULL DEFAULT 1 COMMENT '推送状态：1-成功，2-失败',
  `error_message` varchar(500) COMMENT '错误信息',
  `push_time` datetime NOT NULL COMMENT '推送时间',
  PRIMARY KEY (`id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_enterprise_name` (`enterprise_name`),
  KEY `idx_registration_id` (`registration_id`),
  KEY `idx_push_time` (`push_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警推送日志表';

-- 预警推送批次表
DROP TABLE IF EXISTS `gj_push_batch`;
CREATE TABLE `gj_push_batch` (
  `id` varchar(50) NOT NULL COMMENT '主键',
  `batch_id` varchar(50) NOT NULL COMMENT '批次ID',
  `model_type` int(2) NOT NULL COMMENT '模型类型：1-模型1，2-模型2',
  `total_count` int(10) NOT NULL DEFAULT 0 COMMENT '总推送数量',
  `success_count` int(10) NOT NULL DEFAULT 0 COMMENT '成功推送数量',
  `failed_count` int(10) NOT NULL DEFAULT 0 COMMENT '失败推送数量',
  `skipped_count` int(10) NOT NULL DEFAULT 0 COMMENT '跳过推送数量（去重）',
  `batch_request_data` text COMMENT '批次请求数据',
  `batch_response_data` text COMMENT '批次响应数据',
  `batch_status` int(2) NOT NULL DEFAULT 1 COMMENT '批次状态：1-成功，2-失败',
  `batch_error_message` varchar(500) COMMENT '批次错误信息',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_id` (`batch_id`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警推送批次表';

-- 预警推送去重记录表
DROP TABLE IF EXISTS `gj_push_dedup`;
CREATE TABLE `gj_push_dedup` (
  `id` varchar(50) NOT NULL COMMENT '主键',
  `model_type` int(2) NOT NULL COMMENT '模型类型：1-模型1，2-模型2',
  `dedup_key` varchar(200) NOT NULL COMMENT '去重键：模型1使用企业名称，模型2使用登记ID',
  `enterprise_name` varchar(200) COMMENT '企业名称',
  `enterprise_code` varchar(100) COMMENT '企业编码',
  `registration_id` varchar(50) COMMENT '登记ID',
  `first_push_time` datetime NOT NULL COMMENT '首次推送时间',
  `last_push_time` datetime NOT NULL COMMENT '最后推送时间',
  `push_count` int(10) NOT NULL DEFAULT 1 COMMENT '推送次数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_dedup` (`model_type`, `dedup_key`),
  KEY `idx_first_push_time` (`first_push_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警推送去重记录表';
